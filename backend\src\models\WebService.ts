import mongoose, { Document, Schema } from 'mongoose';

export interface IWebService extends Document {
  key: string;
  label: string;
  subTitle: string;
  icon: string;
  color: string;
  bgGradient : string;
  shadowColor: string;
  route: string;
  badge: string;
  badgeColor: string;
  isActive: boolean;
  order: number;
  createdAt: Date;
  updatedAt: Date;
  special?: Boolean;
}

const ServiceSchema: Schema = new Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  label: {
    type: String,
    required: true,
    trim: true
  },
  subTitle: {
    type: String,
    required: true,
    trim: true
  },
  icon: {
    type: String,
    required: true,
    trim: true
  },
  color: {
    type: String,
    required: true,
    trim: true
  },
  bgGradient: {
    type: String,
    required: true,
    trim: true
  },
  route: {
    type: String,
    required: true,
    trim: true
  },
  badge: {
    type: String,
    required: true,
    trim: true
  },
  badgeColor: {
    type: String,
    required: true,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  special: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Index for efficient queries
ServiceSchema.index({ isActive: 1, order: 1 });

export const Service = mongoose.model<IWebService>('WebService', ServiceSchema);
