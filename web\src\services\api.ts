// Web API Service - mirrors the mobile API service
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export interface WebService {
  _id: string;
  key: string;
  label: string;
  subTitle: string;
  icon: string;
  color: string;
  bgGradient: string;
  shadowColor: string;
  route: string;
  badge: string;
  badgeColor: string;
  isActive: boolean;
  order: number;
  createdAt: Date;
  updatedAt: Date;
  special?: Boolean;
}

export interface SearchResult {
  type: 'service' | 'category' | 'supplier' | 'product';
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  icon?: string;
  color?: string;
  route: string;
  data: any; // Original data object
}

export interface ComprehensiveSearchResults {
  services: SearchResult[];
  categories: SearchResult[];
  suppliers: SearchResult[];
  products: SearchResult[];
  total: number;
}

export interface User {
  id: string;
  email: string;
  role: 'customer' | 'supplier';

  // Basic Information
  firstName: string;
  lastName: string;
  username: string;
  phoneNumber: string;

  // Profile Information
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';

  // Address Information
  address?: string;
  city?: string;
  country?: string;

  // Business Information (for suppliers)
  // supplierId?: string;
  // storeName?: string;
  // businessType?: 'restaurant' | 'clothing' | 'grocery' | 'pharmacy' | 'electronics' | 'other';
  // openHours?: string;

  // Location
  location?: [number, number];

  // Preferences
  notifications: boolean;

  // Verification and Security
  isEmailVerified: boolean;
  lastLogin?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  username: string;
  dateOfBirth: string;
  gender: string;
  address: string;
  city: string;
  country: string;
  role: 'customer' | 'supplier';
  supplierId?: string;
  storeName?: string;
  businessType?: string;
  openHours?: string;
  location?: [number, number]; // [longitude, latitude]
  notifications?: boolean;
}

export interface AuthResponse {
  user: any;
  accessToken: string;
  refreshToken: string;
}

class ApiService {
  private baseURL: string;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';
    this.loadTokensFromStorage();
  }

  private async loadTokensFromStorage(): Promise<void> {
    try {
      this.accessToken = localStorage.getItem('accessToken');
      this.refreshToken = localStorage.getItem('refreshToken');
    } catch (error) {
      console.error('Failed to load tokens from storage:', error);
    }
  }

  private async saveTokensToStorage(accessToken: string, refreshToken: string): Promise<void> {
    try {
      this.accessToken = accessToken;
      this.refreshToken = refreshToken;
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', refreshToken);
    } catch (error) {
      console.error('Failed to save tokens to storage:', error);
    }
  }

  private async clearTokensFromStorage(): Promise<void> {
    try {
      this.accessToken = null;
      this.refreshToken = null;
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
    } catch (error) {
      console.error('Failed to clear tokens from storage:', error);
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Merge existing headers if they exist
      if (options.headers) {
        if (options.headers instanceof Headers) {
          options.headers.forEach((value, key) => {
            headers[key] = value;
          });
        } else if (Array.isArray(options.headers)) {
          options.headers.forEach(([key, value]) => {
            headers[key] = value;
          });
        } else {
          Object.assign(headers, options.headers);
        }
      }

      if (this.accessToken) {
        headers.Authorization = `Bearer ${this.accessToken}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          message: data.message || 'Request failed',
          error: data.error,
        };
      }

      return {
        success: true,
        data: data.data || data,
        message: data.message,
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        message: 'Network error occurred',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await this.makeRequest<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (response.success && response.data && response.data.accessToken && response.data.refreshToken) {
      await this.saveTokensToStorage(response.data.accessToken, response.data.refreshToken);
    }

    return response;
  }

  async signup(userData: SignupRequest): Promise<ApiResponse<any>> {
    const response = await this.makeRequest<any>('/auth/signup', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    return response;
  }

  async logout(): Promise<ApiResponse> {
    const response = await this.makeRequest('/auth/logout', {
      method: 'POST',
      body: JSON.stringify({ refreshToken: this.refreshToken }),
    });

    await this.clearTokensFromStorage();
    return response;
  }

  async forgotPassword(email: string): Promise<ApiResponse> {
    return this.makeRequest('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async verifyResetCode(email: string, code: string): Promise<ApiResponse> {
    return this.makeRequest('/auth/verify-reset-code', {
      method: 'POST',
      body: JSON.stringify({ email, code }),
    });
  }

  async resetPassword(data: { email: string; code: string; newPassword: string }): Promise<ApiResponse> {
    return this.makeRequest('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify({
        email: data.email,
        code: data.code,
        password: data.newPassword
      }),
    });
  }

  async verifyEmail(data: { token: string }): Promise<ApiResponse<AuthResponse>> {
    const response = await this.makeRequest<AuthResponse>('/auth/verify-email', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.success && response.data) {
      await this.saveTokensToStorage(response.data.accessToken, response.data.refreshToken);
    }

    return response;
  }

  async resendVerificationEmail(data: { email: string }): Promise<ApiResponse<void>> {
    return await this.makeRequest<void>('/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getProfile(): Promise<ApiResponse<{ user: User }>> {
    return this.makeRequest<{ user: User }>('/users/profile');
  }

  // Web Services API
  async getWebServices(): Promise<WebService[]> {
    const response = await this.makeRequest<WebService[]>('/web-services');
    return response.data || [];
  }

  async getWebServiceByKey(key: string): Promise<WebService | null> {
    const response = await this.makeRequest<WebService>(`/web-services/${key}`);
    return response.data || null;
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  getAccessToken(): string | null {
    return this.accessToken;
  }
}

export const apiService = new ApiService();

// Helper function to detect if text contains Arabic characters
const containsArabic = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicRegex.test(text);
};

// Helper function to get translated text in the appropriate language
const getTranslatedText = (translationKey: string, defaultText: string, preferredLanguage: 'en' | 'ar'): string => {
  // For web, we'll just return the default text for now
  // TODO: Implement i18n for web application
  return defaultText;
};

// Helper function to check if text matches search term in multiple languages
const matchesSearchTerm = (text: string, searchTerm: string, translationKey?: string): boolean => {
  // Check direct text match
  if (text.toLowerCase().includes(searchTerm)) {
    return true;
  }

  // For web, we'll implement basic search without translation support for now
  // TODO: Implement translation support for web application
  return false;
};

// Comprehensive search function
export const comprehensiveSearch = async (query: string): Promise<ComprehensiveSearchResults> => {
  if (!query.trim()) {
    return {
      services: [],
      categories: [],
      suppliers: [],
      products: [],
      total: 0
    };
  }

  const searchTerm = query.toLowerCase().trim();

  // Detect search language based on the query
  const isArabicSearch = containsArabic(query);
  const preferredLanguage: 'en' | 'ar' = isArabicSearch ? 'ar' : 'en';

  const results: ComprehensiveSearchResults = {
    services: [],
    categories: [],
    suppliers: [],
    products: [],
    total: 0
  };

  try {
    // For now, we'll implement a basic search
    // TODO: Implement full search functionality similar to mobile app
    console.log('Comprehensive search not fully implemented for web yet:', searchTerm, preferredLanguage);

  } catch (error) {
    console.error('Error in comprehensive search:', error);
  }

  return results;
};

// Export individual service functions for convenience
export const getWebServices = () => apiService.getWebServices();
